
from flask import Flask, request, jsonify, make_response, Response, send_file
import smtplib
from email.mime.text import MIMEText
import random
import string
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
import time
import os
import json
import hashlib
import logging
from datetime import datetime, timedelta
import re

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('mail_server')

app = Flask(__name__)
# 添加CORS支持
CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)

# 邮件发送配置
SMTP_SERVER = "smtp.feishu.cn"
SMTP_PORT = 465
EMAIL_USER = "<EMAIL>"
EMAIL_PASS = "aCkzI9Ais8QkHjao"

# 日志文件配置
LOG_DIR = "user_submissions"
LOG_FILE = "all_submissions.txt"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# Token配置
TOKEN_SECRET = "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"  # 用于生成token的密钥
TOKEN_EXPIRY = 3 * 60  # token有效期，3分钟（秒）
MIN_SUBMIT_INTERVAL = 30  # 最小提交间隔时间，30秒
TOKEN_DIR = "tokens"
if not os.path.exists(TOKEN_DIR):
    os.makedirs(TOKEN_DIR)

# 配置自定义限制键
limiter = Limiter(
    get_remote_address,
    app=app,
    default_limits=["2 per day", "1 per hour"],
    storage_uri="memory://",
)


# 保存用户提交信息到文件
def save_submission_to_file(data):
    try:
        # 确保日志目录存在
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR)
        
        # 使用同一个文件保存所有提交
        log_path = os.path.join(LOG_DIR, LOG_FILE)
        
        # 获取当前时间，格式为年月日时分秒
        current_time = datetime.now().strftime("%Y年%m月%d日 %H时%M分%S秒")
        
        # 准备要写入的内容
        submission_info = [
            f"=== 新提交 - {current_time} ===",
            "-" * 50
        ]
        
        for key, value in data.items():
            submission_info.append(f"{key}: {value}")
        
        submission_info.append("-" * 50)
        submission_info.append("\n")  # 添加额外空行分隔不同的提交
        
        # 将信息追加到文件末尾
        with open(log_path, "a", encoding="utf-8") as f:
            f.write("\n".join(submission_info))
                
        return True, log_path
    except Exception as e:
        logger.error(f"保存提交信息失败: {e}")
        return False, str(e)

# 生成和验证token的函数
def generate_token():
    """生成一个包含时间戳和随机字符串的token"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    token_data = f"{timestamp}:{random_str}"
    token = hashlib.sha256((token_data + TOKEN_SECRET).encode()).hexdigest()
    
    # 保存token到文件，用于验证
    token_file = os.path.join(TOKEN_DIR, f"{token}.json")
    with open(token_file, 'w') as f:
        json.dump({"timestamp": timestamp, "random": random_str}, f)
    
    return {"token": token, "timestamp": timestamp}

def verify_token(token, current_timestamp=None):
    """验证token的有效性和时间差"""
    if current_timestamp is None:
        current_timestamp = int(time.time())
    
    token_file = os.path.join(TOKEN_DIR, f"{token}.json")
    
    # 检查token文件是否存在
    if not os.path.exists(token_file):
        return False, "无效的token"
    
    # 读取token信息
    try:
        with open(token_file, 'r') as f:
            token_info = json.load(f)
        
        token_timestamp = token_info.get("timestamp")
        random_str = token_info.get("random")
        
        # 验证token时间是否过期
        if current_timestamp - token_timestamp > TOKEN_EXPIRY:
            os.remove(token_file)  # 删除过期token
            return False, "token已过期"
            
        # 验证提交时间间隔是否过短（防机器人）
        if current_timestamp - token_timestamp < MIN_SUBMIT_INTERVAL:
            return False, "提交过快，请检查填写的信息"
        
        # 验证token的完整性
        token_data = f"{token_timestamp}:{random_str}"
        expected_token = hashlib.sha256((token_data + TOKEN_SECRET).encode()).hexdigest()
        
        if token != expected_token:
            return False, "token异常"
        
        return True, "success"
    
    except Exception as e:
        return False, "token异常"

# 自定义错误处理
@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({"error": "请求过于频繁，请稍后再试"}), 429

@app.route("/send-email", methods=["OPTIONS"])
def handle_options():
    logger.info("handle_options---------------------")
    response = make_response()
    response.headers.add("Access-Control-Allow-Origin", "*")
    response.headers.add("Access-Control-Allow-Headers", "Content-Type,Authorization")
    response.headers.add("Access-Control-Allow-Methods", "GET,POST")
    return response

# 获取token的接口
@app.route("/get-token", methods=["GET"])
@limiter.limit("10/hour;20/day")
def get_token():
    logger.info("get_token---------------------")
    token_data = generate_token()
    return jsonify(token_data)

@app.route("/send-email", methods=["POST"])
@limiter.limit("2/hour;5/day")
def send_email():
    logger.info("send_email---------------------")
    # 同时支持JSON和表单数据
    if request.is_json:
        data = request.json
    else:
        data = request.form
    
    logger.debug(f"接收到的数据: {data}")
    # 验证token
    token = data.get("csrf_token")
    if not token:
        return jsonify({"message": "缺少token参数"}), 400
    
    current_timestamp = int(time.time())
    token_valid, token_message = verify_token(token, current_timestamp)
    
    if not token_valid:
        logger.warning(f"token验证失败: {token_message}")
        return jsonify({"message": token_message}), 401
    
    name = data.get("name")
    email = data.get("email")
    phone = data.get("phone")
    company = data.get("company")
    industry = data.get("industry")
    company_size = data.get("company_size")
    position = data.get("position")
    purpose = data.get("purpose")
    timestamp = data.get("timestamp")
    if not name or not email or not phone or not company or not industry or not company_size or not position or not purpose or not timestamp:
        logger.warning("请求缺少必要参数")
        return jsonify({"message": "缺少必要参数"}), 400

    # 将时间戳转换为年月日时分秒格式
    if timestamp.isdigit():
        # 如果时间戳是毫秒级的，需要转换为秒级
        ts = int(timestamp) / 1000 if len(timestamp) > 10 else int(timestamp)
        formatted_time = datetime.fromtimestamp(ts).strftime("%Y年%m月%d日 %H时%M分%S秒")
        # 更新数据中的时间戳为格式化后的时间
        data = dict(data)
        data["提交时间(格式化)"] = formatted_time

    # 先保存提交信息到本地文件
    save_success, save_result = save_submission_to_file(data)
    if not save_success:
        logger.warning(f"警告：无法保存提交信息到本地文件: {save_result}")
    else:
        logger.info(f"用户提交信息已保存到: {save_result}")

    # 邮件内容
    subject = "审校系统新用户信息提交"
    body = f"姓名: {name}\n邮箱: {email}\n电话: {phone}\n公司: {company}\n行业: {industry}\n公司规模: {company_size}\n职位: {position}\n目的: {purpose}\n提交时间: {formatted_time if 'formatted_time' in locals() else timestamp}"

    msg = MIMEText(body, "plain", "utf-8")
    msg["Subject"] = subject
    msg["From"] = EMAIL_USER
    msg["To"] = EMAIL_USER  # 可以改为接收人邮箱

    try:
        server = smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT)
        server.login(EMAIL_USER, EMAIL_PASS)
        server.sendmail(EMAIL_USER, EMAIL_USER, msg.as_string())
        server.quit()
        
        # 验证成功后使用token，可以删除token文件防止重复使用
        token_file = os.path.join(TOKEN_DIR, f"{token}.json")
        if os.path.exists(token_file):
            os.remove(token_file)
            
        logger.info(f"成功发送邮件给: {EMAIL_USER}")
        return jsonify({"message": "success"})
    except Exception as e:
        error_msg = f"邮件发送失败: {e}"
        logger.error(f"{error_msg}，但用户信息已保存到本地文件")
        return jsonify({"message": error_msg}), 500

@app.route('/video_stream')
@limiter.exempt
def video_stream():
    logger.info("video_stream---------------------")
    """
    视频流媒体接口 - 只返回指定的产品视频
    :return: 视频流响应
    """
    try:
        # 检查 Referer，防止盗链
        referer = request.headers.get('Referer', '')
        logger.debug(f"Referer: {referer}")
        allowed_domains = ['www.publishguard.com']  # 替换为您的域名
        if not any(domain in referer for domain in allowed_domains):
            return jsonify({"error": "未授权的访问"}), 403

        # 固定返回 resource 目录下的 product_video.mp4
        video_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resource')
        video_path = os.path.join(video_dir, 'product_video.mp4')
        
        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.error("产品视频文件不存在")
            return jsonify({"error": "视频文件不存在"}), 404
            
        # 获取文件大小和修改时间
        file_size = os.path.getsize(video_path)
        file_mtime = os.path.getmtime(video_path)
        
        # 生成强ETag
        etag = f'"{hashlib.md5(str(file_mtime).encode()).hexdigest()}"'
        
        # 检查If-None-Match头，支持304响应
        if_none_match = request.headers.get('If-None-Match')
        if if_none_match and if_none_match == etag:
            return '', 304  # Not Modified
        
        # 根据文件大小确定最佳分块大小
        def get_optimal_chunk_size(size):
            if size < 10 * 1024 * 1024:  # 小于10MB
                return 512 * 1024  # 512KB
            elif size < 50 * 1024 * 1024:  # 小于50MB
                return 1 * 1024 * 1024  # 1MB
            else:  # 大文件
                return 2 * 1024 * 1024  # 2MB
                
        optimal_chunk_size = get_optimal_chunk_size(file_size)
        
        # 支持断点续传
        range_header = request.headers.get('Range', None)
        
        if range_header:
            # 解析Range头
            byte_start, byte_end = 0, None
            match = re.search(r'bytes=(\d+)-(\d*)', range_header)
            if match:
                groups = match.groups()
                if groups[0]: byte_start = int(groups[0])
                if groups[1]: byte_end = int(groups[1])
            
            if byte_end is None:
                # 如果是初始请求（从头开始）且未指定结束位置，预加载更多内容
                if byte_start == 0:
                    # 预加载前10MB或整个文件
                    byte_end = min(10 * 1024 * 1024 - 1, file_size - 1)
                else:
                    # 其他情况，预加载接下来的5MB
                    byte_end = min(byte_start + 5 * 1024 * 1024 - 1, file_size - 1)
            else:
                # 如果请求的范围太小，扩大范围以减少请求次数
                if byte_end - byte_start < 1 * 1024 * 1024:  # 如果请求小于1MB
                    byte_end = min(byte_start + 5 * 1024 * 1024 - 1, file_size - 1)
                
            # 计算实际读取的长度
            length = byte_end - byte_start + 1
            
            # 创建部分响应
            response = Response(
                status=206,
                mimetype='video/mp4',
                direct_passthrough=True
            )
            
            # 设置Content-Range头
            response.headers.add('Content-Range', f'bytes {byte_start}-{byte_end}/{file_size}')
            response.headers.add('Accept-Ranges', 'bytes')
            response.headers.add('Content-Length', str(length))
            
            # 定义生成器，使用优化的缓冲区
            def generate_partial():
                with open(video_path, 'rb') as video:
                    video.seek(byte_start)
                    remaining = length
                    chunk_size = min(optimal_chunk_size, length)
                    
                    while remaining > 0:
                        chunk = video.read(min(chunk_size, remaining))
                        if not chunk:
                            break
                        remaining -= len(chunk)
                        yield chunk
                        
            response.response = generate_partial()
            
        else:
            # 完整响应
            response = Response(
                mimetype='video/mp4',
                direct_passthrough=True
            )
            response.headers.add('Content-Length', str(file_size))
            response.headers.add('Accept-Ranges', 'bytes')
            
            # 定义生成器，使用优化的缓冲区
            def generate_full():
                with open(video_path, 'rb') as video:
                    while True:
                        data = video.read(optimal_chunk_size)
                        if not data:
                            break
                        yield data
                    
            response.response = generate_full()

        # 设置头信息，增强缓存策略
        response.headers.add('Content-Disposition', 'inline')
        response.headers.add('Cache-Control', 'public, max-age=86400')  # 允许客户端缓存24小时
        response.headers.add('ETag', etag)
        response.headers.add('X-Frame-Options', 'SAMEORIGIN')
        response.headers.add('X-Content-Type-Options', 'nosniff')
        
        # 添加预加载提示
        response.headers.add('Link', f'<{request.base_url}>; rel=preload; as=video')
        
        return response
        
    except Exception as e:
        logger.error(f"视频流处理错误: {str(e)}")
        return jsonify({"error": "视频流处理失败"}), 500

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)

# 为gunicorn提供WSGI应用入口
# application = app
